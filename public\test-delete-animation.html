<!DOCTYPE html>
<html lang="th">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ทดสอบลูกเล่นการลบรูปภาพ</title>
    <meta name="csrf-token" content="test-token">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 2rem 0;
        }
        
        .demo-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 2rem;
            margin: 2rem auto;
            max-width: 1200px;
        }
        
        .image-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            overflow: hidden;
            transition: all 0.3s ease;
            margin-bottom: 1rem;
        }
        
        .image-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .demo-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 3rem;
        }
        
        .btn-demo {
            border-radius: 25px;
            padding: 0.5rem 1rem;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-demo:hover {
            transform: translateY(-2px);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="demo-container">
            <div class="text-center mb-5">
                <h1 class="display-4 text-primary mb-3">
                    <i class="fas fa-magic me-3"></i>
                    ทดสอบลูกเล่นการลบรูปภาพ
                </h1>
                <p class="lead text-muted">ลองกดปุ่มลบเพื่อดูเอฟเฟกต์สวยงาม</p>
            </div>
            
            <div class="row" id="imageGallery">
                <!-- Sample Images -->
                <div class="col-md-4" id="image-1">
                    <div class="image-card">
                        <div class="demo-image">
                            <i class="fas fa-image"></i>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title mb-2">รูปภาพตัวอย่าง 1</h6>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-demo flex-fill">
                                    <i class="fas fa-eye"></i> ดู
                                </button>
                                <button class="btn btn-danger btn-demo flex-fill" onclick="deleteImage(1)">
                                    <i class="fas fa-trash"></i> ลบ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4" id="image-2">
                    <div class="image-card">
                        <div class="demo-image" style="background: linear-gradient(45deg, #a8edea 0%, #fed6e3 100%);">
                            <i class="fas fa-camera"></i>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title mb-2">รูปภาพตัวอย่าง 2</h6>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-demo flex-fill">
                                    <i class="fas fa-eye"></i> ดู
                                </button>
                                <button class="btn btn-danger btn-demo flex-fill" onclick="deleteImage(2)">
                                    <i class="fas fa-trash"></i> ลบ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4" id="image-3">
                    <div class="image-card">
                        <div class="demo-image" style="background: linear-gradient(45deg, #ffecd2 0%, #fcb69f 100%);">
                            <i class="fas fa-photo-video"></i>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title mb-2">รูปภาพตัวอย่าง 3</h6>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-demo flex-fill">
                                    <i class="fas fa-eye"></i> ดู
                                </button>
                                <button class="btn btn-danger btn-demo flex-fill" onclick="deleteImage(3)">
                                    <i class="fas fa-trash"></i> ลบ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4" id="image-4">
                    <div class="image-card">
                        <div class="demo-image" style="background: linear-gradient(45deg, #ff9a9e 0%, #fecfef 100%);">
                            <i class="fas fa-images"></i>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title mb-2">รูปภาพตัวอย่าง 4</h6>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-demo flex-fill">
                                    <i class="fas fa-eye"></i> ดู
                                </button>
                                <button class="btn btn-danger btn-demo flex-fill" onclick="deleteImage(4)">
                                    <i class="fas fa-trash"></i> ลบ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4" id="image-5">
                    <div class="image-card">
                        <div class="demo-image" style="background: linear-gradient(45deg, #a18cd1 0%, #fbc2eb 100%);">
                            <i class="fas fa-file-image"></i>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title mb-2">รูปภาพตัวอย่าง 5</h6>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-demo flex-fill">
                                    <i class="fas fa-eye"></i> ดู
                                </button>
                                <button class="btn btn-danger btn-demo flex-fill" onclick="deleteImage(5)">
                                    <i class="fas fa-trash"></i> ลบ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-4" id="image-6">
                    <div class="image-card">
                        <div class="demo-image" style="background: linear-gradient(45deg, #fad0c4 0%, #ffd1ff 100%);">
                            <i class="fas fa-picture-o"></i>
                        </div>
                        <div class="card-body p-3">
                            <h6 class="card-title mb-2">รูปภาพตัวอย่าง 6</h6>
                            <div class="d-flex gap-2">
                                <button class="btn btn-outline-primary btn-demo flex-fill">
                                    <i class="fas fa-eye"></i> ดู
                                </button>
                                <button class="btn btn-danger btn-demo flex-fill" onclick="deleteImage(6)">
                                    <i class="fas fa-trash"></i> ลบ
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-5">
                <button class="btn btn-success btn-lg" onclick="resetImages()">
                    <i class="fas fa-redo me-2"></i>รีเซ็ตรูปภาพทั้งหมด
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- Admin Custom JS -->
    <script src="/js/admin-custom.js"></script>
    
    <script>
        // Mock delete function for demo
        async function deleteImage(imageId) {
            await deleteImageWithEffects(
                imageId, 
                `/demo/delete/${imageId}`, // Mock endpoint
                `ลบรูปภาพตัวอย่าง ${imageId} สำเร็จ`
            );
        }
        
        // Reset all images
        function resetImages() {
            location.reload();
        }
        
        // Mock fetch for demo
        const originalFetch = window.fetch;
        window.fetch = function(url, options) {
            if (url.includes('/demo/delete/')) {
                return new Promise((resolve) => {
                    setTimeout(() => {
                        resolve({
                            json: () => Promise.resolve({
                                success: true,
                                message: 'ลบรูปภาพสำเร็จ (Demo)'
                            })
                        });
                    }, 1000); // Simulate network delay
                });
            }
            return originalFetch.apply(this, arguments);
        };
    </script>
</body>
</html>
