// Phuyai Prajak Service Shop Admin Custom JavaScript

document.addEventListener('DOMContentLoaded', function() {

    // Add CSS animations for enhanced UI
    addEnhancedAnimations();

    // Initialize all components with performance optimization
    requestAnimationFrame(() => {
        initThemeToggle();
        initCounterAnimations();
        initCardHoverEffects();
        initFormEnhancements();
        initNotifications();
        initLoadingStates();
        initRealTimeClock();
        initSmoothScrolling();
        initPageTransitions();
        initImageLazyLoading();
        initProgressiveEnhancement();
    });

    console.log('Phuyai Prajak Service Shop Admin initialized successfully! 🚀');
});

// Add enhanced animations CSS
function addEnhancedAnimations() {
    const style = document.createElement('style');
    style.textContent = `
        @keyframes pulse {
            0% { transform: scale(1); opacity: 1; }
            50% { transform: scale(1.05); opacity: 0.7; }
            100% { transform: scale(1); opacity: 1; }
        }

        @keyframes bounce {
            0%, 20%, 53%, 80%, 100% { transform: translateY(0); }
            40%, 43% { transform: translateY(-8px); }
            70% { transform: translateY(-4px); }
            90% { transform: translateY(-2px); }
        }

        @keyframes zoomIn {
            0% { transform: scale(0.3); opacity: 0; }
            50% { opacity: 1; }
            100% { transform: scale(1); }
        }

        @keyframes shrink {
            0% { width: 100%; }
            100% { width: 0%; }
        }

        @keyframes fadeInUp {
            0% { transform: translateY(20px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
            20%, 40%, 60%, 80% { transform: translateX(2px); }
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        .animate-shake {
            animation: shake 0.5s ease-in-out;
        }

        .btn:hover {
            transform: translateY(-1px);
            transition: all 0.2s ease;
        }

        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
            transition: all 0.3s ease;
        }

        .modal-content {
            animation: zoomIn 0.3s ease-out;
        }

        .notification-enter {
            animation: fadeInUp 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .image-delete-loading {
            position: relative;
            overflow: hidden;
        }

        .image-delete-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.4), transparent);
            animation: shimmer 1.5s infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        @keyframes ripple {
            to {
                transform: scale(4);
                opacity: 0;
            }
        }

        @keyframes particle-float {
            0% {
                transform: translate(-50%, -50%) scale(0) rotate(0deg);
                opacity: 1;
            }
            50% {
                transform: translate(-50%, -150px) scale(1) rotate(180deg);
                opacity: 0.8;
            }
            100% {
                transform: translate(-50%, -300px) scale(0) rotate(360deg);
                opacity: 0;
            }
        }

        @keyframes glow {
            0%, 100% { box-shadow: 0 0 5px rgba(40, 167, 69, 0.5); }
            50% { box-shadow: 0 0 20px rgba(40, 167, 69, 0.8), 0 0 30px rgba(40, 167, 69, 0.6); }
        }

        .success-glow {
            animation: glow 1s ease-in-out;
        }

        .delete-btn-loading {
            position: relative;
            overflow: hidden;
        }

        .delete-btn-loading::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: shimmer 1s infinite;
        }
    `;
    document.head.appendChild(style);
}

// Theme Toggle Functionality
function initThemeToggle() {
    const themeToggle = document.querySelector('.theme-toggle');
    const html = document.documentElement;
    const themeIcon = document.getElementById('theme-icon');
    
    // Load saved theme
    const savedTheme = localStorage.getItem('admin-theme') || 'dark';
    html.setAttribute('data-theme', savedTheme);
    
    if (themeIcon) {
        themeIcon.className = savedTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
    }
    
    // Theme toggle event
    if (themeToggle) {
        themeToggle.addEventListener('click', function() {
            const currentTheme = html.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            html.setAttribute('data-theme', newTheme);
            localStorage.setItem('admin-theme', newTheme);
            
            if (themeIcon) {
                themeIcon.className = newTheme === 'dark' ? 'fas fa-moon' : 'fas fa-sun';
            }
            
            // Add transition effect
            document.body.style.transition = 'all 0.3s ease';
            setTimeout(() => {
                document.body.style.transition = '';
            }, 300);
            
            showNotification(`เปลี่ยนเป็น ${newTheme === 'dark' ? 'โหมดมืด' : 'โหมดสว่าง'} แล้ว`, 'success');
        });
    }
}

// Counter Animations
function initCounterAnimations() {
    const counters = document.querySelectorAll('.counter');
    
    const animateCounter = (counter) => {
        const target = parseInt(counter.getAttribute('data-target'));
        const duration = 2000; // 2 seconds
        const increment = target / (duration / 16); // 60fps
        let current = 0;
        
        const updateCounter = () => {
            if (current < target) {
                current += increment;
                counter.textContent = Math.ceil(current);
                requestAnimationFrame(updateCounter);
            } else {
                counter.textContent = target;
            }
        };
        
        updateCounter();
    };
    
    // Intersection Observer for counter animation
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounter(entry.target);
                observer.unobserve(entry.target);
            }
        });
    }, { threshold: 0.5 });
    
    counters.forEach(counter => observer.observe(counter));
}

// Card Hover Effects
function initCardHoverEffects() {
    const cards = document.querySelectorAll('.card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-5px)';
            this.style.transition = 'all 0.3s ease';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
        });
    });
}

// Form Enhancements
function initFormEnhancements() {
    // Add loading state to form submissions
    const forms = document.querySelectorAll('form');
    
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('button[type="submit"]');
            if (submitBtn && !submitBtn.disabled) {
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังดำเนินการ...';
                submitBtn.disabled = true;
                submitBtn.classList.add('btn-loading');
                
                // Re-enable after 10 seconds as fallback
                setTimeout(() => {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    submitBtn.classList.remove('btn-loading');
                }, 10000);
            }
        });
    });
    
    // Enhanced input focus effects
    const inputs = document.querySelectorAll('.form-control');
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });
        
        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
}

// Notification System
function initNotifications() {
    // Auto-hide alerts after 5 seconds
    setTimeout(() => {
        const alerts = document.querySelectorAll('.alert');
        alerts.forEach(alert => {
            if (alert.classList.contains('show')) {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            }
        });
    }, 5000);
}

// Show custom notification
function showNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 9999;
        min-width: 300px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    `;

    notification.innerHTML = `
        <i class="fas fa-${getIconForType(type)} me-2"></i>${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto remove
    setTimeout(() => {
        if (notification.parentElement) {
            const bsAlert = new bootstrap.Alert(notification);
            bsAlert.close();
        }
    }, duration);
}

// Enhanced notification with beautiful animations and icons
function showEnhancedNotification(message, type = 'info', icon = null, duration = 4000) {
    const notificationId = 'notification-' + Date.now();
    const typeColors = {
        success: { bg: 'linear-gradient(135deg, #28a745, #20c997)', icon: 'check-circle', color: '#fff' },
        error: { bg: 'linear-gradient(135deg, #dc3545, #fd7e14)', icon: 'exclamation-triangle', color: '#fff' },
        warning: { bg: 'linear-gradient(135deg, #ffc107, #fd7e14)', icon: 'exclamation-circle', color: '#000' },
        info: { bg: 'linear-gradient(135deg, #17a2b8, #6f42c1)', icon: 'info-circle', color: '#fff' }
    };

    const config = typeColors[type] || typeColors.info;
    const finalIcon = icon || config.icon;

    const notification = document.createElement('div');
    notification.id = notificationId;
    notification.className = 'position-fixed';
    notification.style.cssText = `
        top: 20px;
        right: 20px;
        z-index: 10000;
        min-width: 350px;
        max-width: 400px;
        transform: translateX(100%);
        transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    `;

    notification.innerHTML = `
        <div class="card border-0 shadow-lg" style="border-radius: 15px; overflow: hidden; background: ${config.bg};">
            <div class="card-body p-3 d-flex align-items-center text-white position-relative">
                <div class="position-absolute top-0 start-0 w-100 h-100" style="background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"sparkle\" width=\"20\" height=\"20\" patternUnits=\"userSpaceOnUse\"><circle cx=\"10\" cy=\"10\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.2\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23sparkle)\"/></svg>'); opacity: 0.3;"></div>
                <div class="me-3 position-relative">
                    <div class="notification-icon-bg position-absolute top-50 start-50 translate-middle rounded-circle" style="width: 40px; height: 40px; background: rgba(255,255,255,0.2); animation: pulse 2s infinite;"></div>
                    <i class="fas fa-${finalIcon} position-relative" style="font-size: 1.5rem; color: ${config.color}; z-index: 2; animation: bounce 0.6s ease-in-out;"></i>
                </div>
                <div class="flex-grow-1 position-relative">
                    <div class="fw-bold mb-1" style="color: ${config.color}; font-size: 0.9rem;">${message}</div>
                    <div class="progress" style="height: 3px; background: rgba(255,255,255,0.3);">
                        <div class="progress-bar bg-white" style="width: 100%; animation: shrink ${duration}ms linear;"></div>
                    </div>
                </div>
                <button type="button" class="btn-close btn-close-white ms-2 position-relative" onclick="closeEnhancedNotification('${notificationId}')" style="font-size: 0.8rem;"></button>
            </div>
        </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    requestAnimationFrame(() => {
        notification.style.transform = 'translateX(0)';
    });

    // Auto remove
    setTimeout(() => {
        closeEnhancedNotification(notificationId);
    }, duration);

    return notificationId;
}

// Close enhanced notification
function closeEnhancedNotification(notificationId) {
    const notification = document.getElementById(notificationId);
    if (notification) {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 400);
    }
}

function getIconForType(type) {
    const icons = {
        'success': 'check-circle',
        'danger': 'exclamation-circle',
        'warning': 'exclamation-triangle',
        'info': 'info-circle'
    };
    return icons[type] || 'info-circle';
}

// Loading States
function initLoadingStates() {
    // Show loading overlay
    window.showLoading = function() {
        const overlay = document.createElement('div');
        overlay.className = 'loading-overlay';
        overlay.innerHTML = '<div class="loading-spinner"></div>';
        overlay.id = 'loadingOverlay';
        document.body.appendChild(overlay);
    };
    
    // Hide loading overlay
    window.hideLoading = function() {
        const overlay = document.getElementById('loadingOverlay');
        if (overlay) {
            overlay.remove();
        }
    };
}

// Real-time Clock
function initRealTimeClock() {
    function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleString('th-TH', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
        
        const clockElements = document.querySelectorAll('.real-time-clock');
        clockElements.forEach(element => {
            element.textContent = timeString;
        });
    }
    
    // Update every second
    setInterval(updateClock, 1000);
    updateClock(); // Initial call
}

// Custom Modal Confirmation
function showConfirmModal(title = 'ยืนยันการดำเนินการ', message = 'คุณแน่ใจหรือไม่ที่จะดำเนินการต่อ?', confirmText = 'ยืนยัน', cancelText = 'ยกเลิก') {
    return new Promise((resolve) => {
        // Remove existing modal if any
        const existingModal = document.getElementById('customConfirmModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create modal HTML
        const modalHTML = `
            <div class="modal fade" id="customConfirmModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content border-0 shadow-lg">
                        <div class="modal-header bg-danger text-white border-0">
                            <h5 class="modal-title">
                                <i class="fas fa-exclamation-triangle me-2"></i>${title}
                            </h5>
                        </div>
                        <div class="modal-body p-4">
                            <div class="text-center">
                                <div class="mb-3">
                                    <i class="fas fa-trash-alt text-danger" style="font-size: 3rem;"></i>
                                </div>
                                <p class="mb-0 fs-6">${message}</p>
                            </div>
                        </div>
                        <div class="modal-footer border-0 justify-content-center">
                            <button type="button" class="btn btn-secondary px-4" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>${cancelText}
                            </button>
                            <button type="button" class="btn btn-danger px-4" id="confirmModalBtn">
                                <i class="fas fa-check me-2"></i>${confirmText}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get modal element
        const modal = document.getElementById('customConfirmModal');
        const confirmBtn = document.getElementById('confirmModalBtn');

        // Initialize Bootstrap modal
        const bsModal = new bootstrap.Modal(modal);

        // Handle confirm button click
        confirmBtn.addEventListener('click', () => {
            bsModal.hide();
            resolve(true);
        });

        // Handle modal close (cancel)
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
            resolve(false);
        });

        // Show modal
        bsModal.show();
    });
}

// Enhanced confirm delete function
async function confirmDelete(message = 'คุณแน่ใจหรือไม่ที่จะลบรายการนี้? การกระทำนี้ไม่สามารถยกเลิกได้', title = 'ยืนยันการลบ') {
    return await showConfirmModal(title, message, 'ลบ', 'ยกเลิก');
}

// Enhanced Delete Modal with Beautiful Animations
async function showEnhancedDeleteModal(title = 'ลบรายการ', message = 'คุณแน่ใจหรือไม่ที่จะลบรายการนี้?', subtitle = 'การกระทำนี้ไม่สามารถยกเลิกได้', confirmText = 'ลบ') {
    return new Promise((resolve) => {
        // Remove existing modal if any
        const existingModal = document.getElementById('enhancedDeleteModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Create enhanced modal HTML with animations
        const modalHTML = `
            <div class="modal fade" id="enhancedDeleteModal" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content border-0 shadow-lg" style="border-radius: 20px; overflow: hidden;">
                        <div class="modal-header bg-gradient-danger text-white border-0 position-relative" style="background: linear-gradient(135deg, #dc3545, #c82333);">
                            <div class="position-absolute top-0 start-0 w-100 h-100" style="background: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><defs><pattern id=\"grain\" width=\"100\" height=\"100\" patternUnits=\"userSpaceOnUse\"><circle cx=\"50\" cy=\"50\" r=\"1\" fill=\"%23ffffff\" opacity=\"0.1\"/></pattern></defs><rect width=\"100\" height=\"100\" fill=\"url(%23grain)\"/></svg>'); opacity: 0.3;"></div>
                            <h5 class="modal-title position-relative" style="font-weight: 600;">
                                <i class="fas fa-exclamation-triangle me-2 animate__animated animate__pulse animate__infinite"></i>${title}
                            </h5>
                            <div class="position-absolute end-0 top-50 translate-middle-y me-3">
                                <div class="spinner-border spinner-border-sm text-white-50" role="status" style="animation: spin 3s linear infinite;">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                        <div class="modal-body p-5 text-center">
                            <div class="mb-4">
                                <div class="delete-icon-container position-relative d-inline-block">
                                    <div class="delete-icon-bg position-absolute top-50 start-50 translate-middle rounded-circle bg-danger-subtle" style="width: 80px; height: 80px; animation: pulse 2s infinite;"></div>
                                    <i class="fas fa-trash-alt text-danger position-relative" style="font-size: 2.5rem; z-index: 2; animation: bounce 1s ease-in-out infinite alternate;"></i>
                                </div>
                            </div>
                            <h6 class="mb-3 text-dark" style="font-weight: 600;">${message}</h6>
                            <p class="text-muted mb-0 small">${subtitle}</p>
                        </div>
                        <div class="modal-footer border-0 justify-content-center pb-4">
                            <button type="button" class="btn btn-light px-4 py-2 me-3 shadow-sm" data-bs-dismiss="modal" style="border-radius: 25px; font-weight: 500; transition: all 0.3s ease;">
                                <i class="fas fa-times me-2"></i>ยกเลิก
                            </button>
                            <button type="button" class="btn btn-danger px-4 py-2 shadow-sm" id="enhancedConfirmBtn" style="border-radius: 25px; font-weight: 500; background: linear-gradient(135deg, #dc3545, #c82333); border: none; transition: all 0.3s ease;">
                                <i class="fas fa-check me-2"></i>${confirmText}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Get modal element
        const modal = document.getElementById('enhancedDeleteModal');
        const confirmBtn = document.getElementById('enhancedConfirmBtn');

        // Add hover effects
        const cancelBtn = modal.querySelector('[data-bs-dismiss="modal"]');
        cancelBtn.addEventListener('mouseenter', () => {
            cancelBtn.style.transform = 'translateY(-2px)';
            cancelBtn.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
        });
        cancelBtn.addEventListener('mouseleave', () => {
            cancelBtn.style.transform = 'translateY(0)';
            cancelBtn.style.boxShadow = '0 2px 4px rgba(0,0,0,0.1)';
        });

        confirmBtn.addEventListener('mouseenter', () => {
            confirmBtn.style.transform = 'translateY(-2px)';
            confirmBtn.style.boxShadow = '0 4px 12px rgba(220,53,69,0.4)';
        });
        confirmBtn.addEventListener('mouseleave', () => {
            confirmBtn.style.transform = 'translateY(0)';
            confirmBtn.style.boxShadow = '0 2px 4px rgba(220,53,69,0.2)';
        });

        // Initialize Bootstrap modal
        const bsModal = new bootstrap.Modal(modal);

        // Handle confirm button click
        confirmBtn.addEventListener('click', () => {
            // Add loading state
            confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังลบ...';
            confirmBtn.disabled = true;

            setTimeout(() => {
                bsModal.hide();
                resolve(true);
            }, 800);
        });

        // Handle modal close (cancel)
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
            if (!confirmBtn.disabled) {
                resolve(false);
            }
        });

        // Show modal with animation
        bsModal.show();

        // Add entrance animation
        modal.addEventListener('shown.bs.modal', () => {
            modal.querySelector('.modal-content').style.animation = 'zoomIn 0.3s ease-out';
        });
    });
}

// Enhanced Image Delete with Special Effects
async function deleteImageWithEffects(imageId, endpoint, successMessage = 'ลบรูปภาพสำเร็จ') {
    const confirmed = await showEnhancedDeleteModal(
        'ลบรูปภาพ',
        'คุณแน่ใจหรือไม่ที่จะลบรูปภาพนี้?',
        'การกระทำนี้ไม่สามารถยกเลิกได้',
        'ลบรูปภาพ'
    );

    if (confirmed) {
        const imageElement = document.getElementById(`image-${imageId}`);
        const deleteBtn = imageElement.querySelector('.btn-danger');

        // Create ripple effect
        createRippleEffect(deleteBtn);

        // Add loading state with shimmer
        deleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        deleteBtn.disabled = true;
        imageElement.classList.add('image-delete-loading');

        // Animate image
        imageElement.style.transition = 'all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55)';
        imageElement.style.transform = 'scale(0.95) rotateY(5deg)';
        imageElement.style.opacity = '0.7';
        imageElement.style.filter = 'blur(1px)';

        try {
            const response = await fetch(endpoint, {
                method: 'DELETE',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                }
            });

            const data = await response.json();

            if (data.success) {
                // Success animation sequence
                imageElement.style.transform = 'scale(1.05) rotateY(0deg)';
                imageElement.style.filter = 'brightness(1.2)';

                setTimeout(() => {
                    imageElement.style.transform = 'scale(0) rotateY(90deg)';
                    imageElement.style.opacity = '0';
                    imageElement.style.filter = 'blur(5px)';
                }, 200);

                setTimeout(() => {
                    imageElement.remove();
                    showEnhancedNotification(data.message || successMessage, 'success', 'check-circle');
                    createSuccessParticles();
                }, 600);
            } else {
                // Error animation
                imageElement.classList.add('animate-shake');
                resetImageState(imageElement, deleteBtn);
                showEnhancedNotification(data.message || 'เกิดข้อผิดพลาด', 'error', 'exclamation-triangle');
            }
        } catch (error) {
            console.error('Error:', error);
            imageElement.classList.add('animate-shake');
            resetImageState(imageElement, deleteBtn);
            showEnhancedNotification('เกิดข้อผิดพลาดในการลบรูปภาพ', 'error', 'exclamation-triangle');
        }
    }
}

// Reset image state on error
function resetImageState(imageElement, deleteBtn) {
    setTimeout(() => {
        imageElement.style.transform = 'scale(1)';
        imageElement.style.opacity = '1';
        imageElement.style.filter = 'none';
        imageElement.classList.remove('image-delete-loading', 'animate-shake');
        deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
        deleteBtn.disabled = false;
    }, 500);
}

// Create ripple effect
function createRippleEffect(element) {
    const ripple = document.createElement('span');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);

    ripple.style.cssText = `
        position: absolute;
        border-radius: 50%;
        background: rgba(255,255,255,0.6);
        transform: scale(0);
        animation: ripple 0.6s linear;
        width: ${size}px;
        height: ${size}px;
        left: 50%;
        top: 50%;
        margin-left: -${size/2}px;
        margin-top: -${size/2}px;
        pointer-events: none;
    `;

    element.style.position = 'relative';
    element.style.overflow = 'hidden';
    element.appendChild(ripple);

    setTimeout(() => ripple.remove(), 600);
}

// Create success particles
function createSuccessParticles() {
    for (let i = 0; i < 6; i++) {
        setTimeout(() => {
            const particle = document.createElement('div');
            particle.innerHTML = '✨';
            particle.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                font-size: 20px;
                pointer-events: none;
                z-index: 10001;
                animation: particle-float 2s ease-out forwards;
                transform: translate(-50%, -50%) rotate(${Math.random() * 360}deg);
            `;

            document.body.appendChild(particle);
            setTimeout(() => particle.remove(), 2000);
        }, i * 100);
    }
}

// Utility Functions
window.AdminUtils = {
    // Confirm deletion (updated to use modal)
    confirmDelete: async function(message = 'คุณแน่ใจหรือไม่ที่จะลบรายการนี้?') {
        return await confirmDelete(message);
    },

    // Enhanced image deletion
    deleteImageWithEffects: deleteImageWithEffects,

    // Format number with commas
    formatNumber: function(num) {
        return num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    },

    // Copy to clipboard
    copyToClipboard: function(text) {
        navigator.clipboard.writeText(text).then(() => {
            showEnhancedNotification('คัดลอกแล้ว!', 'success', 'copy', 1500);
        });
    },

    // Smooth scroll to element
    scrollTo: function(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    }
};

// AJAX Helper
window.ajaxRequest = function(url, options = {}) {
    const defaultOptions = {
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
    };
    
    return fetch(url, { ...defaultOptions, ...options })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .catch(error => {
            console.error('AJAX Error:', error);
            showNotification('เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง', 'danger');
            throw error;
        });
};

// Smooth Scrolling
function initSmoothScrolling() {
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
}

// Page Transitions
function initPageTransitions() {
    const pageContent = document.querySelector('.main-wrapper');
    if (pageContent) {
        pageContent.classList.add('page-transition');

        // Trigger transition after DOM is ready
        setTimeout(() => {
            pageContent.classList.add('loaded');
        }, 100);
    }

    // Add loading state to navigation links
    document.querySelectorAll('a:not([href^="#"]):not([target="_blank"])').forEach(link => {
        link.addEventListener('click', function(e) {
            if (this.hostname === window.location.hostname) {
                showPageLoading();
            }
        });
    });
}

// Image Lazy Loading
function initImageLazyLoading() {
    const images = document.querySelectorAll('img[data-src]');

    const imageObserver = new IntersectionObserver((entries, observer) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const img = entry.target;
                img.src = img.dataset.src;
                img.classList.remove('skeleton');
                img.classList.add('image-preview', 'loaded');
                observer.unobserve(img);
            }
        });
    }, {
        rootMargin: '50px'
    });

    images.forEach(img => imageObserver.observe(img));
}

// Progressive Enhancement
function initProgressiveEnhancement() {
    // Add smooth transitions to all interactive elements
    const interactiveElements = document.querySelectorAll('button, .btn, .card, .form-control, .nav-link');

    interactiveElements.forEach(element => {
        element.style.transition = 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)';
    });

    // Add ripple effect to buttons
    document.querySelectorAll('.btn').forEach(button => {
        button.addEventListener('click', createRippleEffect);
    });

    // Enhance form interactions
    document.querySelectorAll('.form-control').forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });
}

// Create Ripple Effect
function createRippleEffect(e) {
    const button = e.currentTarget;
    const ripple = document.createElement('span');
    const rect = button.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = e.clientX - rect.left - size / 2;
    const y = e.clientY - rect.top - size / 2;

    ripple.style.width = ripple.style.height = size + 'px';
    ripple.style.left = x + 'px';
    ripple.style.top = y + 'px';
    ripple.classList.add('ripple');

    // Remove existing ripples
    const existingRipples = button.querySelectorAll('.ripple');
    existingRipples.forEach(r => r.remove());

    button.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Show Page Loading
function showPageLoading() {
    const overlay = document.createElement('div');
    overlay.className = 'loading-overlay show';
    overlay.innerHTML = `
        <div class="text-center">
            <div class="loading-spinner"></div>
            <p class="mt-3 text-muted">กำลังโหลด...</p>
        </div>
    `;
    document.body.appendChild(overlay);
}

// Enhanced Form Validation with Smooth Feedback
function enhanceFormValidation() {
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = true;
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>กำลังบันทึก...';

                // Re-enable after 3 seconds if form doesn't redirect
                setTimeout(() => {
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = submitBtn.dataset.originalText || 'บันทึก';
                }, 3000);
            }
        });
    });
}

// Export for global use
window.showNotification = showNotification;
window.showLoading = showLoading;
window.hideLoading = hideLoading;
window.createRippleEffect = createRippleEffect;
window.showPageLoading = showPageLoading;
