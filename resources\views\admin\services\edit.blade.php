@extends('layouts.admin')

@section('title', 'แก้ไขบริการ - ระบบจัดการ')

@section('breadcrumb')
<li class="breadcrumb-item"><a href="{{ route('admin.services') }}">จัดการบริการ</a></li>
<li class="breadcrumb-item active">แก้ไขบริการ</li>
@endsection

@section('content')
<div class="d-flex justify-content-between align-items-center mb-4">
    <h1 class="h3 mb-0">แก้ไขบริการ: {{ $service->title }}</h1>
    <a href="{{ route('admin.services') }}{{ isset($page) && $page > 1 ? '?page=' . $page : '' }}" class="btn btn-secondary">
        <i class="fas fa-arrow-left me-2"></i>กลับ
    </a>
</div>

<div class="row">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-body">
                <form action="{{ route('admin.services.update', $service->id) }}" method="POST" enctype="multipart/form-data">
                    @csrf
                    @method('PUT')
                    <input type="hidden" name="page" value="{{ $page ?? 1 }}">
                    
                    <div class="mb-3">
                        <label for="title" class="form-label">ชื่อบริการ <span class="text-danger">*</span></label>
                        <input type="text" class="form-control @error('title') is-invalid @enderror" 
                               id="title" name="title" value="{{ old('title', $service->title) }}" required>
                        @error('title')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">คำอธิบายสั้น <span class="text-danger">*</span></label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" name="description" rows="3" required>{{ old('description', $service->description) }}</textarea>
                        @error('description')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="details" class="form-label">รายละเอียด</label>
                        <textarea class="form-control @error('details') is-invalid @enderror" 
                                  id="details" name="details" rows="5">{{ old('details', $service->details) }}</textarea>
                        @error('details')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="sort_order" class="form-label">ลำดับการแสดง</label>
                        <input type="number" class="form-control @error('sort_order') is-invalid @enderror"
                               id="sort_order" name="sort_order" value="{{ old('sort_order', $service->sort_order) }}" min="0">
                        @error('sort_order')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <!-- Current Gallery -->
                    @if($service->images->count() > 0)
                    <div class="mb-4">
                        <label class="form-label">แกลเลอรี่รูปภาพปัจจุบัน</label>
                        <div class="row g-2" id="currentGallery">
                            @foreach($service->images->sortBy('sort_order') as $image)
                            <div class="col-md-3 col-4" id="image-{{ $image->id }}">
                                <div class="card position-relative">
                                    <img src="{{ asset('storage/' . $image->image_path) }}"
                                         class="card-img-top"
                                         alt="{{ $image->alt_text }}"
                                         style="height: 120px; object-fit: cover; cursor: pointer;"
                                         onclick="viewImage('{{ asset('storage/' . $image->image_path) }}', '{{ $image->alt_text }}')">

                                    @if($image->is_cover)
                                    <div class="position-absolute top-0 end-0 m-1">
                                        <span class="badge bg-primary">รูปปก</span>
                                    </div>
                                    @endif

                                    <div class="card-body p-2">
                                        <div class="d-flex gap-1">
                                            @if(!$image->is_cover)
                                            <button type="button" class="btn btn-sm btn-outline-primary flex-fill"
                                                    onclick="setCoverImage({{ $image->id }})"
                                                    title="ตั้งเป็นรูปปก">
                                                <i class="fas fa-star"></i>
                                            </button>
                                            @endif
                                            <button type="button" class="btn btn-sm btn-outline-danger flex-fill"
                                                    onclick="deleteImage({{ $image->id }})"
                                                    title="ลบรูป">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @endforeach
                        </div>
                    </div>
                    @endif

                    <div class="mb-3">
                        <label for="image" class="form-label">รูปภาพหลักใหม่ <span class="text-muted">(เปลี่ยนรูปปก)</span></label>
                        <input type="file" class="form-control @error('image') is-invalid @enderror"
                               id="image" name="image" accept="image/*">
                        @error('image')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB (เลือกไฟล์ใหม่หากต้องการเปลี่ยนรูปปก)</div>
                    </div>

                    <div class="mb-3">
                        <label for="new_gallery_images" class="form-label">เพิ่มรูปภาพแกลเลอรี่ใหม่</label>
                        <input type="file" class="form-control @error('new_gallery_images.*') is-invalid @enderror"
                               id="new_gallery_images" name="new_gallery_images[]" accept="image/*" multiple>
                        @error('new_gallery_images.*')
                        <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        <div class="form-text">
                            <i class="fas fa-info-circle text-primary me-1"></i>
                            สามารถเลือกหลายรูปพร้อมกัน | รองรับไฟล์: JPG, PNG, GIF ขนาดไม่เกิน 2MB ต่อรูป
                        </div>

                        <!-- Preview Area -->
                        <div id="new-gallery-preview" class="mt-3" style="display: none;">
                            <label class="form-label">ตัวอย่างรูปใหม่ที่เลือก:</label>
                            <div class="row g-2" id="new-preview-container"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" 
                                   value="1" {{ old('is_active', $service->is_active) ? 'checked' : '' }}>
                            <label class="form-check-label" for="is_active">
                                เปิดใช้งาน
                            </label>
                        </div>
                    </div>
                    
                    <div class="d-flex gap-2">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>บันทึกการแก้ไข
                        </button>
                        <a href="{{ route('admin.services') }}{{ isset($page) && $page > 1 ? '?page=' . $page : '' }}" class="btn btn-secondary">ยกเลิก</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-lg-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">ข้อมูลบริการ</h5>
            </div>
            <div class="card-body">
                <table class="table table-sm">
                    <tr>
                        <td><strong>สร้างเมื่อ:</strong></td>
                        <td>{{ $service->created_at->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>แก้ไขล่าสุด:</strong></td>
                        <td>{{ $service->updated_at->format('d/m/Y H:i') }}</td>
                    </tr>
                    <tr>
                        <td><strong>สถานะ:</strong></td>
                        <td>
                            @if($service->is_active)
                            <span class="badge bg-success">เปิดใช้งาน</span>
                            @else
                            <span class="badge bg-secondary">ปิดใช้งาน</span>
                            @endif
                        </td>
                    </tr>
                </table>
            </div>
        </div>
        
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">การดำเนินการ</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('services') }}" class="btn btn-outline-primary" target="_blank">
                        <i class="fas fa-eye me-2"></i>ดูในเว็บไซต์
                    </a>
                    <form action="{{ route('admin.services.delete', $service->id) }}" method="POST"
                          id="deleteServiceForm{{ $service->id }}">
                        @csrf
                        @method('DELETE')
                        <button type="button" class="btn btn-outline-danger w-100"
                                onclick="handleDeleteService({{ $service->id }})">
                            <i class="fas fa-trash me-2"></i>ลบบริการ
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

@section('scripts')
<script>
// Delete service function with custom modal
async function handleDeleteService(serviceId) {
    const confirmed = await confirmDelete(
        'คุณแน่ใจหรือไม่ที่จะลบบริการนี้? การกระทำนี้ไม่สามารถยกเลิกได้',
        'ยืนยันการลบบริการ'
    );

    if (confirmed) {
        document.getElementById(`deleteServiceForm${serviceId}`).submit();
    }
}

// Image Gallery Management
function viewImage(src, title) {
    const modal = new bootstrap.Modal(document.getElementById('imageModal'));
    document.getElementById('modalImage').src = src;
    document.getElementById('modalTitle').textContent = title;
    document.getElementById('modalDescription').textContent = '';
    modal.show();
}

async function deleteImage(imageId) {
    await deleteImageWithEffects(
        imageId,
        `/admin/services/images/${imageId}`,
        'ลบรูปภาพบริการสำเร็จ'
    );
}

function setCoverImage(imageId) {
    if (confirm('ตั้งรูปนี้เป็นรูปปกหรือไม่?')) {
        fetch(`/admin/services/images/${imageId}/set-cover`, {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                location.reload(); // Reload to update cover badges
            } else {
                showNotification(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showNotification('เกิดข้อผิดพลาดในการตั้งรูปปก', 'error');
        });
    }
}

function showNotification(message, type) {
    const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
    const notification = document.createElement('div');
    notification.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// New gallery images preview
document.addEventListener('DOMContentLoaded', function() {
    const newGalleryInput = document.getElementById('new_gallery_images');
    const newPreviewArea = document.getElementById('new-gallery-preview');
    const newPreviewContainer = document.getElementById('new-preview-container');

    if (newGalleryInput) {
        newGalleryInput.addEventListener('change', function(e) {
            const files = Array.from(e.target.files);

            if (files.length > 0) {
                newPreviewArea.style.display = 'block';
                newPreviewContainer.innerHTML = '';

                files.forEach((file, index) => {
                    if (file.type.startsWith('image/')) {
                        const reader = new FileReader();

                        reader.onload = function(e) {
                            const col = document.createElement('div');
                            col.className = 'col-md-2 col-4';

                            col.innerHTML = `
                                <div class="card">
                                    <img src="${e.target.result}" class="card-img-top"
                                         style="height: 100px; object-fit: cover;"
                                         alt="Preview ${index + 1}">
                                    <div class="card-body p-2">
                                        <small class="text-muted">รูปใหม่ที่ ${index + 1}</small>
                                    </div>
                                </div>
                            `;

                            newPreviewContainer.appendChild(col);
                        };

                        reader.readAsDataURL(file);
                    }
                });
            } else {
                newPreviewArea.style.display = 'none';
            }
        });
    }
});
</script>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="modalTitle">รูปภาพ</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="" class="img-fluid rounded mb-3" style="max-height: 500px;">
                <p id="modalDescription" class="text-muted"></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">ปิด</button>
            </div>
        </div>
    </div>
</div>

@endsection
